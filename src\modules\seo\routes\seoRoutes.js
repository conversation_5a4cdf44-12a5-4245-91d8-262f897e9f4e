const express = require('express');
const router = express.Router();
const seoController = require('../controllers/seoController');
const seoValidators = require('../validators/seoValidator');
const authMiddleware = require('../../../middleware/auth');
const { authorize } = require('../../../middleware/permission');

/**
 * @swagger
 * components:
 *   schemas:
 *     UpsertSeoMetaRequest:
 *       type: object
 *       properties:
 *         metaTitle:
 *           type: string
 *           description: SEO meta title
 *           example: "Best Web Development Services | Company Name"
 *         metaDescription:
 *           type: string
 *           description: SEO meta description
 *           example: "Professional web development services with modern technologies. Get your website built by experts."
 *         metaKeywords:
 *           type: string
 *           description: SEO meta keywords (comma-separated)
 *           example: "web development, website design, react, nodejs"
 *         canonicalUrl:
 *           type: string
 *           description: Canonical URL
 *           example: "https://example.com/services/web-development"
 *         ogTitle:
 *           type: string
 *           description: Open Graph title
 *           example: "Best Web Development Services"
 *         ogDescription:
 *           type: string
 *           description: Open Graph description
 *           example: "Professional web development services with modern technologies"
 *         ogImage:
 *           type: string
 *           description: Open Graph image URL
 *           example: "https://example.com/images/og-web-dev.jpg"
 *         ogType:
 *           type: string
 *           description: Open Graph type
 *           example: "website"
 *         twitterCard:
 *           type: string
 *           enum: [summary, summary_large_image, app, player]
 *           description: Twitter card type
 *           example: "summary_large_image"
 *         twitterTitle:
 *           type: string
 *           description: Twitter title
 *           example: "Best Web Development Services"
 *         twitterDescription:
 *           type: string
 *           description: Twitter description
 *           example: "Professional web development services"
 *         twitterImage:
 *           type: string
 *           description: Twitter image URL
 *           example: "https://example.com/images/twitter-web-dev.jpg"
 *         schemaMarkup:
 *           type: object
 *           description: JSON-LD schema markup
 *           example: {"@context": "https://schema.org", "@type": "Service"}
 *         robots:
 *           type: string
 *           description: Robots meta tag
 *           example: "index,follow"
 *         focusKeyword:
 *           type: string
 *           description: Primary focus keyword
 *           example: "web development"
 *         customMeta:
 *           type: object
 *           description: Custom meta fields
 *           example: {"author": "John Doe", "category": "Technology"}
 *     SeoMetaResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SeoMeta'
 *     SeoMetaListResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: "SEO metas retrieved successfully"
 *         data:
 *           type: object
 *           properties:
 *             seoMetas:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/SeoMetaResponse'
 *             pagination:
 *               type: object
 *               properties:
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 limit:
 *                   type: integer
 *                   example: 10
 *                 total:
 *                   type: integer
 *                   example: 25
 *                 pages:
 *                   type: integer
 *                   example: 3
 *     SeoAnalyticsResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: "SEO analytics retrieved successfully"
 *         data:
 *           type: object
 *           properties:
 *             total:
 *               type: integer
 *               example: 150
 *             averageScore:
 *               type: integer
 *               example: 75
 *             scoreDistribution:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   category:
 *                     type: string
 *                     example: "excellent"
 *                   count:
 *                     type: integer
 *                     example: 45
 *             byEntityType:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   entityType:
 *                     type: string
 *                     example: "post"
 *                   count:
 *                     type: integer
 *                     example: 80
 *                   averageScore:
 *                     type: integer
 *                     example: 78
 */

/**
 * @swagger
 * /api/seo:
 *   get:
 *     summary: Get all SEO metas
 *     description: Retrieve a paginated list of SEO metas with filtering and sorting options
 *     tags: [SEO]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search query for meta title, description, keywords
 *       - in: query
 *         name: entityType
 *         schema:
 *           type: string
 *           enum: [post, page, category, tag, user]
 *         description: Filter by entity type
 *       - in: query
 *         name: focusKeyword
 *         schema:
 *           type: string
 *         description: Filter by focus keyword
 *       - in: query
 *         name: minSeoScore
 *         schema:
 *           type: integer
 *           minimum: 0
 *           maximum: 100
 *         description: Minimum SEO score
 *       - in: query
 *         name: maxSeoScore
 *         schema:
 *           type: integer
 *           minimum: 0
 *           maximum: 100
 *         description: Maximum SEO score
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: created_at
 *         description: Sort field
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: DESC
 *         description: Sort order
 *     responses:
 *       200:
 *         description: SEO metas retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SeoMetaListResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/',
  authMiddleware,
  authorize('seo.read'),
  seoValidators.getSeoMetasQuery,
  seoController.getSeoMetas
);

/**
 * @swagger
 * /api/seo/analytics:
 *   get:
 *     summary: Get SEO analytics
 *     description: Get SEO analytics and statistics
 *     tags: [SEO]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: SEO analytics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SeoAnalyticsResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/analytics',
  authMiddleware,
  authorize('seo.read'),
  seoController.getSeoAnalytics
);

module.exports = router;
